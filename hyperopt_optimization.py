"""
HyperOpt Optimization for Backtesting Strategy
This file performs hyperparameter optimization for the backtesting strategy
without changing the core logic of the existing system.
"""

import os
import sys
import time
import tempfile
import shutil
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from hyperopt import hp, fmin, tpe, Trials, STATUS_OK, STATUS_FAIL
import logger_config

# Import the main backtesting functions
from BT_Thursday_MainCode import run_for_one_day
from load_parquet_in_memory import load_parquet_data, list_date_folders

logger = logger_config.setup_logger('hyperopt_logger', 'hyperopt.log')

class HyperOptBacktester:
    def __init__(self, root_path, start_date_str, end_date_str):
        """
        Initialize the HyperOpt Backtester
        
        Args:
            root_path: Path to the parquet files
            start_date_str: Start date in YYYYMMDD format
            end_date_str: End date in YYYYMMDD format
        """
        self.root_path = root_path
        self.start_date_str = start_date_str
        self.end_date_str = end_date_str
        self.original_env = {}
        
        # Load data once for all optimizations
        logger.info("Loading data for optimization...")
        try:
            from_date = datetime.strptime(start_date_str, "%Y%m%d")
            to_date = datetime.strptime(end_date_str, "%Y%m%d")
            all_folders = list_date_folders(root_path, from_date, to_date)
            self.data = load_parquet_data(all_folders)
            logger.info(f"Loaded data for {len(self.data)} trading days")
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            raise
    
    def backup_environment(self):
        """Backup current environment variables"""
        env_vars = [
            'STOP_LOSS_PERCENTAGE', 'TARGET_PROFIT_PERCENTAGE', 
            'ratio_check', 'RESIZE_THURSDAY'
        ]
        for var in env_vars:
            if var in os.environ:
                self.original_env[var] = os.environ[var]
    
    def restore_environment(self):
        """Restore original environment variables"""
        for var, value in self.original_env.items():
            os.environ[var] = value
    
    def set_hyperparameters(self, params):
        """
        Set environment variables based on hyperparameters
        
        Args:
            params: Dictionary containing hyperparameter values
        """
        # Convert start_time and exit_time to proper format
        start_time_str = params['start_time']
        exit_time_str = params['exit_time']
        
        # Set environment variables
        os.environ['STOP_LOSS_PERCENTAGE'] = str(params['stop_loss'])
        os.environ['TARGET_PROFIT_PERCENTAGE'] = str(params['target_profit'])
        os.environ['ratio_check'] = str(params['ratio_check'])
        os.environ['RESIZE_THURSDAY'] = str(params['resize_factor'])
        
        logger.info(f"Set hyperparameters: {params}")
        
        return start_time_str, exit_time_str
    
    def objective_function(self, params):
        """
        Objective function for hyperparameter optimization
        
        Args:
            params: Dictionary containing hyperparameter values
            
        Returns:
            Dictionary with loss value and status
        """
        try:
            logger.info(f"Evaluating parameters: {params}")
            
            # Set hyperparameters
            start_time_str, exit_time_str = self.set_hyperparameters(params)
            
            # Convert time strings to time objects
            start_time = datetime.strptime(start_time_str, "%H:%M").time()
            exit_time = datetime.strptime(exit_time_str, "%H:%M").time()
            
            total_pnl_sum = 0
            valid_days = 0
            exit_reason_counts = {
                "STOP_LOSS": 0,
                "TARGET_PROFIT": 0,
                "RATIO_HIT": 0,
                "TIME_EXIT": 0
            }
            
            # Run backtesting for all days
            for mainkey, subfolders in self.data.items():
                try:
                    result = run_for_one_day(mainkey, subfolders)
                    
                    if result is None or len(result) < 2:
                        continue
                        
                    pnl, exit_reason = result[0], result[1]
                    
                    if pnl is not None:
                        total_pnl_sum += pnl
                        valid_days += 1
                        
                        if exit_reason in exit_reason_counts:
                            exit_reason_counts[exit_reason] += 1
                            
                except Exception as e:
                    logger.warning(f"Error processing day {mainkey}: {e}")
                    continue
            
            if valid_days == 0:
                logger.warning("No valid trading days found")
                return {'loss': float('inf'), 'status': STATUS_FAIL}
            
            # Calculate metrics
            avg_pnl = total_pnl_sum / valid_days
            
            # Calculate additional metrics for better optimization
            win_rate = (exit_reason_counts.get("TARGET_PROFIT", 0) / valid_days) * 100
            loss_rate = (exit_reason_counts.get("STOP_LOSS", 0) / valid_days) * 100
            
            # Objective: Maximize total PnL (minimize negative PnL)
            # We can also incorporate other metrics like Sharpe ratio, win rate, etc.
            loss = -total_pnl_sum  # Negative because hyperopt minimizes
            
            logger.info(f"Results - Total PnL: {total_pnl_sum:.2f}, Avg PnL: {avg_pnl:.2f}, "
                       f"Win Rate: {win_rate:.1f}%, Loss Rate: {loss_rate:.1f}%, Valid Days: {valid_days}")
            
            return {
                'loss': loss,
                'status': STATUS_OK,
                'eval_time': time.time(),
                'total_pnl': total_pnl_sum,
                'avg_pnl': avg_pnl,
                'valid_days': valid_days,
                'win_rate': win_rate,
                'loss_rate': loss_rate,
                'exit_reasons': exit_reason_counts
            }
            
        except Exception as e:
            logger.error(f"Error in objective function: {e}")
            return {'loss': float('inf'), 'status': STATUS_FAIL}

def create_search_space(space_type='default'):
    """
    Create the hyperparameter search space

    Args:
        space_type: Type of search space ('default', 'tight', 'wide', 'continuous')

    Returns:
        Dictionary defining the search space
    """
    from hyperopt_config import OptimizationConfig

    if space_type == 'tight':
        return OptimizationConfig.get_tight_search_space()
    elif space_type == 'wide':
        return OptimizationConfig.get_wide_search_space()
    elif space_type == 'continuous':
        return OptimizationConfig.get_continuous_search_space()
    else:
        return OptimizationConfig.get_default_search_space()

def run_optimization(max_evals=None, space_type='default', save_results=True):
    """
    Run the hyperparameter optimization

    Args:
        max_evals: Maximum number of evaluations (uses config default if None)
        space_type: Type of search space to use
        save_results: Whether to save results to CSV
    """
    from hyperopt_config import OptimizationConfig

    if max_evals is None:
        max_evals = OptimizationConfig.MAX_EVALS

    logger.info("Starting hyperparameter optimization...")
    logger.info(f"Search space type: {space_type}")
    logger.info(f"Max evaluations: {max_evals}")

    # Configuration
    root_path = OptimizationConfig.ROOT_PATH
    start_date_str = os.getenv("START_DATE", "20210101")
    end_date_str = os.getenv("END_DATE", "20210110")

    logger.info(f"Date range: {start_date_str} to {end_date_str}")

    # Initialize backtester
    backtester = HyperOptBacktester(root_path, start_date_str, end_date_str)
    backtester.backup_environment()

    try:
        # Create search space
        space = create_search_space(space_type)

        # Initialize trials object to store results
        trials = Trials()

        # Run optimization
        logger.info(f"Running optimization with {max_evals} evaluations...")
        start_time = time.time()

        best = fmin(
            fn=backtester.objective_function,
            space=space,
            algo=tpe.suggest,
            max_evals=max_evals,
            trials=trials,
            verbose=True
        )

        optimization_time = time.time() - start_time

        # Log results
        logger.info(f"Optimization completed in {optimization_time:.2f} seconds")
        logger.info(f"Best parameters: {best}")

        # Get the best trial details
        best_trial = min(trials.trials, key=lambda x: x['result']['loss'])
        best_result = best_trial['result']

        logger.info("="*50)
        logger.info("OPTIMIZATION RESULTS")
        logger.info("="*50)
        logger.info(f"Best Total PnL: {best_result.get('total_pnl', 'N/A')}")
        logger.info(f"Best Avg PnL: {best_result.get('avg_pnl', 'N/A')}")
        logger.info(f"Win Rate: {best_result.get('win_rate', 'N/A')}%")
        logger.info(f"Loss Rate: {best_result.get('loss_rate', 'N/A')}%")
        logger.info(f"Valid Days: {best_result.get('valid_days', 'N/A')}")
        logger.info(f"Exit Reasons: {best_result.get('exit_reasons', 'N/A')}")

        # Save results to CSV
        if save_results:
            save_results_to_csv(trials, best, space_type)

        return best, trials

    finally:
        # Restore original environment
        backtester.restore_environment()

def save_results_to_csv(trials, best_params, space_type='default'):
    """Save optimization results to CSV file"""
    results_data = []

    for trial in trials.trials:
        if trial['result']['status'] == STATUS_OK:
            result = trial['result']
            params = trial['misc']['vals']

            # Convert hyperopt parameter format to readable format
            row = {
                'total_pnl': result.get('total_pnl', 0),
                'avg_pnl': result.get('avg_pnl', 0),
                'win_rate': result.get('win_rate', 0),
                'loss_rate': result.get('loss_rate', 0),
                'valid_days': result.get('valid_days', 0),
                'loss': result.get('loss', float('inf')),
                'space_type': space_type
            }

            # Add parameter values
            for param_name, param_values in params.items():
                if param_values:
                    row[param_name] = param_values[0]

            results_data.append(row)

    # Create DataFrame and save
    df = pd.DataFrame(results_data)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"hyperopt_results_{space_type}_{timestamp}.csv"
    df.to_csv(filename, index=False)
    logger.info(f"Results saved to {filename}")

    # Also save a summary of the best result
    if results_data:
        best_row = min(results_data, key=lambda x: x['loss'])
        summary_filename = f"hyperopt_best_{space_type}_{timestamp}.txt"

        with open(summary_filename, 'w') as f:
            f.write("HYPEROPT OPTIMIZATION SUMMARY\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"Search Space Type: {space_type}\n")
            f.write(f"Total Evaluations: {len(results_data)}\n")
            f.write(f"Best Total PnL: {best_row['total_pnl']:.2f}\n")
            f.write(f"Best Avg PnL: {best_row['avg_pnl']:.2f}\n")
            f.write(f"Win Rate: {best_row['win_rate']:.1f}%\n")
            f.write(f"Loss Rate: {best_row['loss_rate']:.1f}%\n")
            f.write(f"Valid Days: {best_row['valid_days']}\n\n")
            f.write("Best Parameters:\n")
            for key, value in best_row.items():
                if key not in ['total_pnl', 'avg_pnl', 'win_rate', 'loss_rate', 'valid_days', 'loss', 'space_type']:
                    f.write(f"  {key}: {value}\n")

        logger.info(f"Summary saved to {summary_filename}")

if __name__ == "__main__":
    # Run optimization
    best_params, trials = run_optimization(max_evals=10)
    
    print("\nOptimization completed!")
    print(f"Best parameters found: {best_params}")
