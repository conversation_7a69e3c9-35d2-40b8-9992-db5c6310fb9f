# HyperOpt Optimization for Backtesting Strategy

This hyperparameter optimization system allows you to tune your backtesting strategy parameters without changing the core logic. It uses the HyperOpt library to efficiently search through parameter combinations and find optimal settings.

## Files Overview

- **`hyperopt_optimization.py`** - Main optimization engine
- **`hyperopt_config.py`** - Configuration and search space definitions
- **`run_hyperopt.py`** - Easy-to-use runner script with CLI and interactive modes
- **`hyperopt_requirements.txt`** - Required Python packages
- **`HYPEROPT_README.md`** - This documentation file

## Installation

1. Install the required packages:
```bash
pip install -r hyperopt_requirements.txt
```

2. Ensure your existing backtesting environment is set up with all necessary dependencies.

## Quick Start

### Interactive Mode (Recommended for beginners)
```bash
python run_hyperopt.py
```
This will start an interactive menu where you can select different optimization modes.

### Command Line Mode
```bash
# Quick optimization (10 evaluations)
python run_hyperopt.py --mode quick

# Thorough optimization (50 evaluations)
python run_hyperopt.py --mode thorough

# Custom optimization
python run_hyperopt.py --mode custom --max-evals 25 --space-type tight

# Show search space information
python run_hyperopt.py --mode info
```

## Optimization Parameters

The system optimizes the following parameters:

| Parameter | Description | Default Range |
|-----------|-------------|---------------|
| `start_time` | Trading start time | 09:30 to 09:55 (5-min intervals) |
| `exit_time` | Trading exit time | 14:45 to 15:15 (5-min intervals) |
| `stop_loss` | Stop loss percentage | -0.5% to -1.5% (0.1% steps) |
| `target_profit` | Target profit percentage | 1.0% to 2.0% (0.1% steps) |
| `ratio_check` | Ratio check threshold | 0.10 to 0.20 (0.01 steps) |
| `resize_factor` | Position resize factor | 0.3 to 0.7 (0.1 steps) |

## Search Space Types

### 1. Default Space
- Uses the exact parameters specified in your request
- Balanced exploration of parameter space
- Good for initial optimization runs

### 2. Tight Space
- Focused around known good values
- Smaller parameter ranges
- Good for fine-tuning

### 3. Wide Space
- Broader parameter exploration
- Larger ranges for discovery
- Good for initial exploration

### 4. Continuous Space
- Uses continuous distributions instead of discrete choices
- More granular optimization
- Good for final optimization

## Optimization Modes

### Quick Mode
- 10 evaluations
- Default search space
- Fast results for testing

### Thorough Mode
- 50 evaluations
- Default search space
- More comprehensive optimization

### Tight Mode
- 20 evaluations
- Tight search space
- Fine-tuning around good parameters

### Wide Mode
- 30 evaluations
- Wide search space
- Broad exploration

### Continuous Mode
- 25 evaluations
- Continuous search space
- Granular optimization

### Custom Mode
- User-defined evaluations and space type
- Maximum flexibility

## Configuration

Edit `hyperopt_config.py` to customize:

- Search space ranges
- Number of evaluations
- Optimization algorithm
- Objective function weights
- File paths

## Results

The optimization produces several output files:

1. **`hyperopt_results_[space_type]_[timestamp].csv`** - Detailed results for all trials
2. **`hyperopt_best_[space_type]_[timestamp].txt`** - Summary of best parameters
3. **`hyperopt.log`** - Detailed optimization log
4. **`hyperopt_runner.log`** - Runner script log

## Example Usage

### Basic Optimization
```python
from hyperopt_optimization import run_optimization

# Run optimization with default settings
best_params, trials = run_optimization(max_evals=10)
print(f"Best parameters: {best_params}")
```

### Advanced Usage
```python
from hyperopt_optimization import HyperOptBacktester, create_search_space
from hyperopt import fmin, tpe, Trials

# Create custom backtester
backtester = HyperOptBacktester(
    root_path="path/to/data",
    start_date_str="20210101", 
    end_date_str="20210110"
)

# Create custom search space
space = create_search_space('tight')

# Run optimization
trials = Trials()
best = fmin(
    fn=backtester.objective_function,
    space=space,
    algo=tpe.suggest,
    max_evals=20,
    trials=trials
)
```

## Environment Variables

The system uses these environment variables (set in your `.env` file):

- `START_DATE` - Backtest start date (YYYYMMDD)
- `END_DATE` - Backtest end date (YYYYMMDD)
- `CAPITAL` - Trading capital
- `GLOBAL_QTY` - Global quantity
- `WEEKDAY_NUMBERS` - Weekday mapping

## Tips for Effective Optimization

1. **Start with Quick Mode** - Get initial results fast
2. **Use Tight Mode** - After finding good parameters, fine-tune with tight space
3. **Monitor Logs** - Check log files for detailed progress
4. **Save Results** - Keep track of different optimization runs
5. **Validate Results** - Test optimized parameters on out-of-sample data

## Troubleshooting

### Common Issues

1. **Import Errors** - Ensure all dependencies are installed
2. **Data Loading Errors** - Check file paths and data availability
3. **Memory Issues** - Reduce max_evals or date range for large datasets
4. **Environment Variables** - Ensure .env file is properly configured

### Performance Tips

1. Use smaller date ranges for faster optimization
2. Start with fewer evaluations and increase gradually
3. Monitor system resources during optimization
4. Use tight search space for faster convergence

## Integration with Existing Code

The optimization system is designed to work with your existing backtesting code without modifications:

- Uses the same environment variables
- Calls your existing functions (`run_for_one_day`, `thursday_execute_BCS`, `monitor_prices`)
- Preserves original environment after optimization
- No changes to core backtesting logic

## Next Steps

1. Run a quick optimization to test the system
2. Analyze results and adjust search spaces if needed
3. Run thorough optimization with best-performing space type
4. Validate optimized parameters on new data
5. Implement best parameters in your production system

For questions or issues, check the log files for detailed error information.
