# HyperOpt Optimization Requirements
# Install these packages for hyperparameter optimization functionality

# Core optimization library
hyperopt>=0.2.7

# Data manipulation and analysis
pandas>=1.3.0
numpy>=1.21.0

# Plotting and visualization (optional, for result analysis)
matplotlib>=3.5.0
seaborn>=0.11.0

# Progress bars and utilities
tqdm>=4.62.0

# Scientific computing
scipy>=1.7.0

# Configuration and environment
python-dotenv>=0.19.0

# Logging enhancements (optional)
colorlog>=6.6.0
