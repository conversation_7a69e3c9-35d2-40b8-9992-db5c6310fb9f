"""
Simple runner script for HyperOpt optimization
This script provides an easy interface to run different optimization scenarios
"""

import os
import sys
import argparse
from datetime import datetime
import logger_config
from hyperopt_optimization import run_optimization
from hyperopt_config import OptimizationConfig, print_search_space_info, get_total_combinations

def setup_environment():
    """Setup environment variables if not already set"""
    # Set default environment variables if not present
    env_defaults = {
        'START_DATE': '20210101',
        'END_DATE': '20210110',
        'GLOBAL_QTY': '75',
        'CAPITAL': '200000',
        'WEEKDAY_NUMBERS': '{"MONDAY": 0, "TUESDAY": 1, "WEDNESDAY": 2, "THURSDAY": 3, "FRIDAY": 4}'
    }
    
    for key, value in env_defaults.items():
        if key not in os.environ:
            os.environ[key] = value
            print(f"Set default {key} = {value}")

def run_quick_optimization():
    """Run a quick optimization with 10 evaluations"""
    print("Running quick optimization (10 evaluations)...")
    return run_optimization(max_evals=10, space_type='default')

def run_thorough_optimization():
    """Run a thorough optimization with 50 evaluations"""
    print("Running thorough optimization (50 evaluations)...")
    return run_optimization(max_evals=50, space_type='default')

def run_tight_optimization():
    """Run optimization with tight search space"""
    print("Running optimization with tight search space...")
    return run_optimization(max_evals=20, space_type='tight')

def run_wide_optimization():
    """Run optimization with wide search space"""
    print("Running optimization with wide search space...")
    return run_optimization(max_evals=30, space_type='wide')

def run_continuous_optimization():
    """Run optimization with continuous search space"""
    print("Running optimization with continuous search space...")
    return run_optimization(max_evals=25, space_type='continuous')

def run_custom_optimization(max_evals, space_type):
    """Run custom optimization"""
    print(f"Running custom optimization ({max_evals} evaluations, {space_type} space)...")
    return run_optimization(max_evals=max_evals, space_type=space_type)

def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(description='HyperOpt Optimization Runner')
    parser.add_argument('--mode', choices=['quick', 'thorough', 'tight', 'wide', 'continuous', 'custom', 'info'], 
                       default='quick', help='Optimization mode')
    parser.add_argument('--max-evals', type=int, default=10, help='Maximum evaluations (for custom mode)')
    parser.add_argument('--space-type', choices=['default', 'tight', 'wide', 'continuous'], 
                       default='default', help='Search space type (for custom mode)')
    parser.add_argument('--start-date', type=str, help='Start date (YYYYMMDD format)')
    parser.add_argument('--end-date', type=str, help='End date (YYYYMMDD format)')
    
    args = parser.parse_args()
    
    # Setup environment
    setup_environment()
    
    # Override dates if provided
    if args.start_date:
        os.environ['START_DATE'] = args.start_date
    if args.end_date:
        os.environ['END_DATE'] = args.end_date
    
    print(f"Date range: {os.environ['START_DATE']} to {os.environ['END_DATE']}")
    
    # Setup logger
    logger = logger_config.setup_logger('hyperopt_runner', 'hyperopt_runner.log')
    
    try:
        if args.mode == 'info':
            print_search_space_info()
            print(f"\nTotal possible combinations: {get_total_combinations():,}")
            return
        
        print(f"\nStarting {args.mode} optimization...")
        print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("-" * 50)
        
        # Run the appropriate optimization
        if args.mode == 'quick':
            best_params, trials = run_quick_optimization()
        elif args.mode == 'thorough':
            best_params, trials = run_thorough_optimization()
        elif args.mode == 'tight':
            best_params, trials = run_tight_optimization()
        elif args.mode == 'wide':
            best_params, trials = run_wide_optimization()
        elif args.mode == 'continuous':
            best_params, trials = run_continuous_optimization()
        elif args.mode == 'custom':
            best_params, trials = run_custom_optimization(args.max_evals, args.space_type)
        
        print("\n" + "="*50)
        print("OPTIMIZATION COMPLETED SUCCESSFULLY!")
        print("="*50)
        print(f"Best parameters found: {best_params}")
        print(f"Total trials completed: {len(trials.trials)}")
        
        # Show best result details
        best_trial = min(trials.trials, key=lambda x: x['result']['loss'])
        best_result = best_trial['result']
        
        print(f"\nBest Result Details:")
        print(f"  Total PnL: {best_result.get('total_pnl', 'N/A')}")
        print(f"  Average PnL: {best_result.get('avg_pnl', 'N/A')}")
        print(f"  Win Rate: {best_result.get('win_rate', 'N/A')}%")
        print(f"  Loss Rate: {best_result.get('loss_rate', 'N/A')}%")
        print(f"  Valid Days: {best_result.get('valid_days', 'N/A')}")
        
        print(f"\nResults have been saved to CSV files.")
        print(f"Check the current directory for hyperopt_results_*.csv files.")
        
    except Exception as e:
        print(f"\nERROR: Optimization failed with error: {e}")
        logger.error(f"Optimization failed: {e}")
        sys.exit(1)

def interactive_mode():
    """Interactive mode for easier usage"""
    print("HyperOpt Optimization Interactive Mode")
    print("=" * 40)
    
    setup_environment()
    
    print(f"\nCurrent settings:")
    print(f"  Date range: {os.environ.get('START_DATE')} to {os.environ.get('END_DATE')}")
    print(f"  Capital: {os.environ.get('CAPITAL')}")
    print(f"  Global Qty: {os.environ.get('GLOBAL_QTY')}")
    
    print(f"\nAvailable optimization modes:")
    print(f"  1. Quick (10 evaluations, default space)")
    print(f"  2. Thorough (50 evaluations, default space)")
    print(f"  3. Tight (20 evaluations, tight space)")
    print(f"  4. Wide (30 evaluations, wide space)")
    print(f"  5. Continuous (25 evaluations, continuous space)")
    print(f"  6. Custom")
    print(f"  7. Show search space info")
    
    choice = input("\nSelect mode (1-7): ").strip()
    
    try:
        if choice == '1':
            best_params, trials = run_quick_optimization()
        elif choice == '2':
            best_params, trials = run_thorough_optimization()
        elif choice == '3':
            best_params, trials = run_tight_optimization()
        elif choice == '4':
            best_params, trials = run_wide_optimization()
        elif choice == '5':
            best_params, trials = run_continuous_optimization()
        elif choice == '6':
            max_evals = int(input("Enter max evaluations: "))
            space_type = input("Enter space type (default/tight/wide/continuous): ").strip()
            if space_type not in ['default', 'tight', 'wide', 'continuous']:
                space_type = 'default'
            best_params, trials = run_custom_optimization(max_evals, space_type)
        elif choice == '7':
            print_search_space_info()
            return
        else:
            print("Invalid choice!")
            return
        
        print(f"\nOptimization completed! Best parameters: {best_params}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    if len(sys.argv) == 1:
        # No command line arguments, run interactive mode
        interactive_mode()
    else:
        # Command line arguments provided, run CLI mode
        main()
