"""
Configuration file for HyperOpt Optimization
Adjust these settings to customize the optimization process
"""

from hyperopt import hp

class OptimizationConfig:
    """Configuration class for hyperparameter optimization"""
    
    # Optimization settings
    MAX_EVALS = 10  # Number of optimization iterations
    ALGORITHM = 'tpe'  # Tree-structured Parzen Estimator
    
    # Data settings
    ROOT_PATH = r"C:\Users\<USER>\YatinBhatia\Thursday_BackTesting_DataFrames_V2 - Copy\Parquet_Files\Thursday_output_folder"
    
    # Search space configuration
    SEARCH_SPACE_CONFIG = {
        'start_time': {
            'type': 'choice',
            'options': [f"09:{m:02d}" for m in range(30, 60, 5)],
            'description': 'Trading start time options'
        },
        'exit_time': {
            'type': 'choice', 
            'options': [f"14:{m:02d}" for m in range(45, 60, 5)] + [f"15:{m:02d}" for m in range(0, 20, 5)],
            'description': 'Trading exit time options'
        },
        'stop_loss': {
            'type': 'choice',
            'options': [-0.5 - 0.1 * i for i in range(11)],  # -0.5 to -1.5
            'description': 'Stop loss percentage options'
        },
        'target_profit': {
            'type': 'choice',
            'options': [1.0 + 0.1 * i for i in range(11)],  # 1.0 to 2.0
            'description': 'Target profit percentage options'
        },
        'ratio_check': {
            'type': 'choice',
            'options': [0.10 + 0.01 * i for i in range(11)],  # 0.10 to 0.20
            'description': 'Ratio check threshold options'
        },
        'resize_factor': {
            'type': 'choice',
            'options': [0.3 + 0.1 * i for i in range(5)],  # 0.3 to 0.7
            'description': 'Resize factor options'
        }
    }
    
    # Alternative search spaces for different optimization strategies
    
    @classmethod
    def get_tight_search_space(cls):
        """Tighter search space around current best-known values"""
        return {
            'start_time': hp.choice('start_time', ["09:40", "09:45", "09:50"]),
            'exit_time': hp.choice('exit_time', ["15:00", "15:05", "15:10"]),
            'stop_loss': hp.choice('stop_loss', [-1.3, -1.4, -1.5, -1.6]),
            'target_profit': hp.choice('target_profit', [0.8, 0.9, 1.0, 1.1, 1.2]),
            'ratio_check': hp.choice('ratio_check', [0.17, 0.18, 0.19, 0.20, 0.21]),
            'resize_factor': hp.choice('resize_factor', [0.6, 0.7, 0.8])
        }
    
    @classmethod
    def get_wide_search_space(cls):
        """Wider search space for exploration"""
        return {
            'start_time': hp.choice('start_time', [f"09:{m:02d}" for m in range(15, 60, 5)]),
            'exit_time': hp.choice('exit_time', [f"14:{m:02d}" for m in range(30, 60, 5)] + 
                                                [f"15:{m:02d}" for m in range(0, 30, 5)]),
            'stop_loss': hp.choice('stop_loss', [-0.3 - 0.1 * i for i in range(15)]),  # -0.3 to -1.7
            'target_profit': hp.choice('target_profit', [0.5 + 0.1 * i for i in range(20)]),  # 0.5 to 2.4
            'ratio_check': hp.choice('ratio_check', [0.05 + 0.01 * i for i in range(20)]),  # 0.05 to 0.24
            'resize_factor': hp.choice('resize_factor', [0.2 + 0.1 * i for i in range(8)])  # 0.2 to 0.9
        }
    
    @classmethod
    def get_continuous_search_space(cls):
        """Continuous search space using uniform distributions"""
        return {
            'start_time_minutes': hp.uniform('start_time_minutes', 30, 59),  # 09:30 to 09:59
            'exit_time_minutes': hp.uniform('exit_time_minutes', 885, 920),  # 14:45 to 15:20 (in minutes from midnight)
            'stop_loss': hp.uniform('stop_loss', -2.0, -0.3),
            'target_profit': hp.uniform('target_profit', 0.5, 2.5),
            'ratio_check': hp.uniform('ratio_check', 0.05, 0.25),
            'resize_factor': hp.uniform('resize_factor', 0.2, 1.0)
        }
    
    @classmethod
    def get_default_search_space(cls):
        """Default search space as specified in the original request"""
        return {
            'start_time': hp.choice('start_time', cls.SEARCH_SPACE_CONFIG['start_time']['options']),
            'exit_time': hp.choice('exit_time', cls.SEARCH_SPACE_CONFIG['exit_time']['options']),
            'stop_loss': hp.choice('stop_loss', cls.SEARCH_SPACE_CONFIG['stop_loss']['options']),
            'target_profit': hp.choice('target_profit', cls.SEARCH_SPACE_CONFIG['target_profit']['options']),
            'ratio_check': hp.choice('ratio_check', cls.SEARCH_SPACE_CONFIG['ratio_check']['options']),
            'resize_factor': hp.choice('resize_factor', cls.SEARCH_SPACE_CONFIG['resize_factor']['options'])
        }
    
    # Objective function settings
    OBJECTIVE_WEIGHTS = {
        'total_pnl': 1.0,      # Primary objective
        'win_rate': 0.0,       # Secondary objective (set to 0 to ignore)
        'sharpe_ratio': 0.0,   # Tertiary objective (set to 0 to ignore)
        'max_drawdown': 0.0    # Risk metric (set to 0 to ignore)
    }
    
    # Logging settings
    LOG_LEVEL = 'INFO'
    SAVE_DETAILED_RESULTS = True
    SAVE_PLOTS = True
    
    # Performance settings
    PARALLEL_JOBS = 1  # Set to -1 to use all available cores (not recommended for this use case)
    
    # Early stopping settings
    EARLY_STOPPING = {
        'enabled': False,
        'patience': 5,  # Stop if no improvement for 5 consecutive trials
        'min_improvement': 0.01  # Minimum improvement threshold
    }

# Utility functions for configuration
def print_search_space_info():
    """Print information about the search space"""
    config = OptimizationConfig.SEARCH_SPACE_CONFIG
    
    print("Search Space Configuration:")
    print("=" * 50)
    
    for param_name, param_config in config.items():
        print(f"\n{param_name.upper()}:")
        print(f"  Description: {param_config['description']}")
        print(f"  Type: {param_config['type']}")
        print(f"  Options: {param_config['options']}")
        print(f"  Count: {len(param_config['options'])}")

def get_total_combinations():
    """Calculate total number of possible combinations"""
    config = OptimizationConfig.SEARCH_SPACE_CONFIG
    total = 1
    
    for param_config in config.values():
        total *= len(param_config['options'])
    
    return total

if __name__ == "__main__":
    print_search_space_info()
    print(f"\nTotal possible combinations: {get_total_combinations():,}")
    print(f"Configured max evaluations: {OptimizationConfig.MAX_EVALS}")
    print(f"Coverage: {(OptimizationConfig.MAX_EVALS / get_total_combinations()) * 100:.2f}%")
